package at.aau.se2.cluedo.controllers;

import at.aau.se2.cluedo.dto.GameStartedResponse;
import at.aau.se2.cluedo.models.gameboard.GameBoard;
import at.aau.se2.cluedo.models.gameobjects.Player;
import at.aau.se2.cluedo.models.gameobjects.SecretFile;
import at.aau.se2.cluedo.services.GameService;
import at.aau.se2.cluedo.services.LobbyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.stereotype.Controller;

import java.util.List;


@Controller
public class GameplayController {


    @Autowired
    private LobbyService lobbyService;

    @Autowired
    private GameService gameService;

    // Note: makeSuggestion and makeAccusation methods moved to TurnController
    // to avoid WebSocket mapping conflicts and provide better turn-based logic



    @MessageMapping("/displayGameBoard/{lobbyId}")
    @SendTo("/topic/displayedGameBoard/{lobbyId}")
    public String displayGameBoard(@DestinationVariable String lobbyId, List<Player> players) {
        gameService.getGame(lobbyId).getGameBoard().displayGameBoard(players);
        return lobbyId;
    }
    @MessageMapping("/getGameBoard/{lobbyId}")
    @SendTo("/topic/gotGameBoard/{lobbyId}")
    public GameBoard getGameBoard(@DestinationVariable String lobbyId) {
        return gameService.getGame(lobbyId).getGameBoard();
    }
}
